"""
UI功能模块
提供UI界面相关的功能函数和数据处理，优化线程安全和性能
"""

from PyQt5.QtCore import QMetaObject, Qt, QTimer
import threading


# UI更新防抖缓存
_ui_update_cache = {}
_ui_update_timer = None
_ui_update_lock = threading.Lock()


def send_device_info_to_ui(connection_info, thread_ref, login_server=""):
    """
    发送设备信息到UI界面（线程安全，带防抖）
    
    Args:
        connection_info (dict): 设备连接信息
        thread_ref: 线程引用对象，用于发送信号
        login_server (str): 登录区服信息，默认为空
        
    Returns:
        dict: 格式化后的设备信息
    """
    if connection_info and thread_ref:
        # 格式化设备信息
        table_device_info = {
            "device_id": connection_info.get('android_id', ''),
            "ip": connection_info.get('device_ip', ''),
            "group": connection_info.get('group_id', ''),
            "identifier": connection_info.get('identifier', ''),
            "account": "",  # 登录账号信息待后续获取
            "server": login_server,
            "map": "",  # 当前地图信息待后续获取
            "character": "",  # 角色名信息待后续获取
            "level": "",  # 等级信息待后续获取
            "action": "设备连接完成",
            "mode": connection_info.get('mode', ''),
            "status": "已连接"
        }
        
        # 使用线程安全的方式发送信号
        _send_signal_safe(thread_ref.device_info_updated, table_device_info)
        print(f"设备信息已发送到UI界面: IP={table_device_info['ip']}, ID={table_device_info['device_id']}")
        return table_device_info
    return None


def _send_signal_safe(signal, data):
    """
    线程安全地发送PyQt信号
    
    Args:
        signal: PyQt信号对象
        data: 要发送的数据
    """
    # PyQt信号本身已经是线程安全的，直接emit即可
    signal.emit(data)


def batch_update_ui_with_debounce(update_func, data, delay_ms=300):
    """
    批量更新UI，带防抖机制
    
    Args:
        update_func: 更新函数
        data: 更新数据
        delay_ms: 防抖延迟毫秒数
    """
    global _ui_update_timer, _ui_update_cache
    
    with _ui_update_lock:
        # 将数据加入缓存
        device_id = data.get('device_id', '')
        if device_id:
            _ui_update_cache[device_id] = data
        
        # 重置定时器
        if _ui_update_timer:
            _ui_update_timer.stop()
        
        # 创建新的定时器
        _ui_update_timer = QTimer()
        _ui_update_timer.setSingleShot(True)
        _ui_update_timer.timeout.connect(lambda: _flush_ui_updates(update_func))
        _ui_update_timer.start(delay_ms)


def _flush_ui_updates(update_func):
    """
    批量刷新UI更新
    
    Args:
        update_func: 更新函数
    """
    global _ui_update_cache
    
    with _ui_update_lock:
        # 批量处理所有缓存的更新
        if _ui_update_cache:
            for device_data in _ui_update_cache.values():
                update_func(device_data)
            _ui_update_cache.clear()


def optimize_ui_performance():
    """
    UI性能优化配置
    """
    from PyQt5.QtWidgets import QApplication
    
    app = QApplication.instance()
    if app:
        # 设置优化属性
        app.setAttribute(Qt.AA_DontCreateNativeWidgetSiblings, True)
        app.setAttribute(Qt.AA_DontShowIconsInMenus, True)
        # 减少重绘频率
        app.setEffectEnabled(Qt.UI_AnimateMenu, False)
        app.setEffectEnabled(Qt.UI_AnimateCombo, False)
        app.setEffectEnabled(Qt.UI_AnimateTooltip, False)